package com.bxm.customer.domain.vo.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 账务类型信息VO
 */
@Data
@ApiModel("账务类型信息VO")
public class AccountingInfoVO {

    @NotNull(message = "账务主类型不能为空")
    @ApiModelProperty(value = "账务主类型：1-标准账务，2-非标账务", required = true)
    private AccountMainType mainType;

    @ApiModelProperty(value = "账务子类型列表（当主类型为非标时）")
    private List<AccountSubType> subTypes;
}